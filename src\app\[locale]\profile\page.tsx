'use client'

import { useEffect, useState } from 'react'
import { useTranslations } from 'next-intl'
import { useUser } from '@/providers/UserProvider'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Plus, Trash2, User, MapPin, Globe, Link } from 'lucide-react'
import { api } from '@/services/api'
import { ProfileData, SocialLink } from '@/services/types'
import toast from '@/utils/toast'

export default function ProfilePage() {
  const t = useTranslations('profile')
  const { userData, refreshUser } = useUser()
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [profileData, setProfileData] = useState<ProfileData>({
    bio: '',
    location: '',
    website: '',
    socialLinks: [],
  })

  // 加载用户配置数据
  useEffect(() => {
    const loadProfile = async () => {
      if (!userData) return

      try {
        const response = await api.user.getProfile()
        if (response.success && response.data) {
          setProfileData(response.data)
        }
      } catch (error) {
        console.error('Failed to load profile:', error)
      } finally {
        setLoading(false)
      }
    }

    loadProfile()
  }, [userData])

  // 处理基本信息输入变化
  const handleBasicInfoChange = (
    field: keyof Pick<ProfileData, 'bio' | 'location' | 'website'>,
    value: string
  ) => {
    setProfileData((prev) => ({
      ...prev,
      [field]: value,
    }))
  }

  // 添加社交链接
  const addSocialLink = () => {
    setProfileData((prev) => ({
      ...prev,
      socialLinks: [
        ...(prev.socialLinks || []),
        { platform: 'custom', url: '', label: '' },
      ],
    }))
  }

  // 更新社交链接
  const updateSocialLink = (
    index: number,
    field: keyof SocialLink,
    value: string
  ) => {
    setProfileData((prev) => ({
      ...prev,
      socialLinks:
        prev.socialLinks?.map((link, i) =>
          i === index ? { ...link, [field]: value } : link
        ) || [],
    }))
  }

  // 删除社交链接
  const removeSocialLink = (index: number) => {
    setProfileData((prev) => ({
      ...prev,
      socialLinks: prev.socialLinks?.filter((_, i) => i !== index) || [],
    }))
  }

  // 保存配置
  const handleSave = async () => {
    if (!userData) return

    setSaving(true)
    try {
      const response = await api.user.updateProfile(profileData)
      if (response.success) {
        toast.success(t('saved'))
        // 刷新用户数据
        await refreshUser()
      } else {
        toast.error(t('error'))
      }
    } catch (error) {
      console.error('Failed to save profile:', error)
      toast.error(t('error'))
    } finally {
      setSaving(false)
    }
  }

  // 如果用户未登录，显示提示
  if (!userData) {
    return (
      <div className="min-h-screen pt-28 px-8">
        <div className="max-w-2xl mx-auto text-center">
          <h1 className="text-2xl font-bold mb-4">请先登录</h1>
          <p className="text-gray-600">您需要登录后才能访问个人设置页面。</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen pt-28 px-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">{t('title')}</h1>

        {/* 用户基本信息 */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <div className="w-12 h-12 rounded-full overflow-hidden ring-2 ring-slate-200">
                <img
                  src={userData.image}
                  alt="Avatar"
                  className="w-full h-full object-cover"
                />
              </div>
              <div>
                <h2 className="text-xl font-semibold">{userData.name}</h2>
                <p className="text-sm text-gray-600">{userData.email}</p>
              </div>
            </CardTitle>
          </CardHeader>
        </Card>

        {loading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
            <p className="mt-2 text-gray-600">加载中...</p>
          </div>
        ) : (
          <div className="grid gap-8 md:grid-cols-2">
            {/* 基本信息 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="w-5 h-5" />
                  {t('basicInfo')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* 个人简介 */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">
                    {t('bio')}
                  </label>
                  <Textarea
                    placeholder={t('placeholder.bio')}
                    value={profileData.bio || ''}
                    onChange={(e) =>
                      handleBasicInfoChange('bio', e.target.value)
                    }
                    className="min-h-[100px] resize-none"
                  />
                </div>

                {/* 所在地 */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                    <MapPin className="w-4 h-4" />
                    {t('location')}
                  </label>
                  <Input
                    type="text"
                    placeholder={t('placeholder.location')}
                    value={profileData.location || ''}
                    onChange={(e) =>
                      handleBasicInfoChange('location', e.target.value)
                    }
                  />
                </div>

                {/* 个人网站 */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                    <Globe className="w-4 h-4" />
                    {t('website')}
                  </label>
                  <Input
                    type="url"
                    placeholder={t('placeholder.website')}
                    value={profileData.website || ''}
                    onChange={(e) =>
                      handleBasicInfoChange('website', e.target.value)
                    }
                  />
                </div>
              </CardContent>
            </Card>

            {/* 社交链接 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Link className="w-5 h-5" />
                    {t('socialLinks')}
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={addSocialLink}
                    className="flex items-center gap-2"
                  >
                    <Plus className="w-4 h-4" />
                    {t('addSocialLink')}
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {profileData.socialLinks &&
                profileData.socialLinks.length > 0 ? (
                  profileData.socialLinks.map((link, index) => (
                    <div key={index} className="flex items-center gap-3 ">
                      <div className="flex-1 grid grid-cols-2 gap-3">
                        {/* 标签 */}
                        <Input
                          type="text"
                          placeholder={t('placeholder.label')}
                          value={link.label || ''}
                          onChange={(e) =>
                            updateSocialLink(index, 'label', e.target.value)
                          }
                          className="text-sm"
                        />

                        {/* URL */}
                        <Input
                          type="url"
                          placeholder={t('placeholder.url')}
                          value={link.url}
                          onChange={(e) =>
                            updateSocialLink(index, 'url', e.target.value)
                          }
                          className="text-sm"
                        />
                      </div>

                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeSocialLink(index)}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50 flex-shrink-0"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-6 text-gray-500">
                    <Link className="w-6 h-6 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">暂无链接</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        )}

        {/* 保存按钮 */}
        {!loading && (
          <div className="mt-8">
            <Button
              onClick={handleSave}
              disabled={saving}
              className="w-full bg-slate-900 hover:bg-slate-800 text-white"
              size="lg"
            >
              {saving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  保存中...
                </>
              ) : (
                t('save')
              )}
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}
